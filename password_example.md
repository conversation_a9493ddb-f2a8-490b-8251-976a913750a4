# TSSH 密码安全优化

## 问题描述
当使用命令行 `tssh -o'Password xxx' user@host` 时，密码会暴露在命令行中，存在安全风险。

## 解决方案
通过修改参数解析逻辑，支持从标准输入安全地读取密码，避免密码在命令行中暴露。

## 使用方法

### 1. 原有方式（不推荐，密码会暴露）
```bash
tssh -o'Password mypassword123' user@host
```

### 2. 新的安全方式（推荐）
```bash
tssh -o'Password -' user@host
```
当使用 `-` 作为密码值时，程序会提示用户输入密码，密码输入过程中不会显示在屏幕上。

## 实现细节

### 修改的文件
- `tssh/args.go`: 在 `UnmarshalText` 方法中添加了密码安全处理逻辑
- `tssh/args_test.go`: 添加了相应的测试用例

### 核心逻辑
```go
// 安全处理密码参数：如果值为 "-"，则从标准输入读取
if strings.ToLower(key) == "password" && value == "-" {
    fmt.Fprintf(os.Stderr, "Enter password: ")
    password, err := readPasswordFromStdin()
    if err != nil {
        return fmt.Errorf("failed to read password: %v", err)
    }
    value = password
}
```

### 优势
1. **安全性**: 密码不会出现在命令行历史中
2. **兼容性**: 完全向后兼容，不影响现有功能
3. **简洁性**: 只需要将密码值改为 `-` 即可
4. **最小改动**: 只修改了参数解析逻辑，不影响业务代码

## 测试验证
```bash
# 编译
go build -o tssh.exe ./cmd/tssh

# 运行测试
go test -v ./tssh -run TestPasswordStdinOption
```

## 注意事项
- 当使用 `-` 作为密码值时，程序会等待用户输入
- 输入密码时不会显示字符，这是正常的安全行为
- 输入完成后按回车键确认
