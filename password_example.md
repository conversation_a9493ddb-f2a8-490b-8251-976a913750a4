# TSSH 密码安全优化

## 问题描述
当使用命令行 `tssh -o'Password xxx' user@host` 时，密码会暴露在命令行中，存在安全风险。其他进程可以通过 `ps` 或 `tasklist` 命令看到完整的命令行参数，包括明文密码。

## 解决方案
通过在程序启动后立即清除命令行参数中的敏感信息，防止密码暴露在进程列表中。

## 使用方法

### 完全兼容的安全方式
```bash
tssh -o'Password mypassword123' user@host
```
**现在这样使用是安全的！** 程序会在启动后立即将命令行中的密码替换为 `***`，其他进程看到的是：
```bash
tssh -o'Password ***' user@host
```

## 实现细节

### 修改的文件
- `tssh/args.go`: 在 `UnmarshalText` 方法中添加了密码安全处理逻辑
- `tssh/args_test.go`: 添加了相应的测试用例

### 核心逻辑
```go
// ClearSensitiveArgs 清除命令行参数中的敏感信息，防止密码暴露
func ClearSensitiveArgs() {
    for i, arg := range os.Args {
        // 检查是否包含密码参数
        if strings.Contains(strings.ToLower(arg), "password=") {
            // 找到等号位置
            if pos := strings.Index(arg, "="); pos >= 0 {
                // 保留参数名，清除密码值
                os.Args[i] = arg[:pos+1] + "***"
            }
        }
        // 处理其他密码格式...
    }
}
```

### 优势
1. **完全兼容**: 无需改变任何使用方式，现有命令完全兼容
2. **自动保护**: 程序启动后自动清除敏感信息
3. **进程安全**: 其他进程无法通过进程列表看到明文密码
4. **最小改动**: 只修改了参数处理逻辑，业务代码完全不变

## 测试验证
```bash
# 编译
go build -o tssh.exe ./cmd/tssh

# 运行密码清除功能测试
go run test_password_clear.go

# 运行安全演示
go run demo_password_security.go
```

## 支持的密码格式
程序会自动清除以下格式中的密码：
- `-oPassword=secret123`
- `-o Password=secret123`
- `-oPassword secret123`
- `-o password=secret123`

## 安全效果
**使用前（不安全）:**
```bash
$ ps aux | grep tssh
user  1234  tssh -oPassword=secret123 user@host
```

**使用后（安全）:**
```bash
$ ps aux | grep tssh
user  1234  tssh -oPassword=*** user@host
```
