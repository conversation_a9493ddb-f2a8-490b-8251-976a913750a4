module github.com/trzsz/trzsz-ssh

go 1.24

require (
	github.com/Microsoft/go-winio v0.6.2
	github.com/alessio/shellescape v1.4.2
	github.com/charmbracelet/bubbles v0.21.0
	github.com/charmbracelet/bubbletea v1.3.4
	github.com/charmbracelet/lipgloss v1.1.0
	github.com/chzyer/readline v1.5.1
	github.com/creack/pty v1.1.24
	github.com/google/shlex v0.0.0-20191202100458-e7afc7fbc510
	github.com/mattn/go-isatty v0.0.20
	github.com/mattn/go-runewidth v0.0.16
	github.com/mitchellh/go-homedir v1.1.0
	github.com/pquerna/otp v1.4.0
	github.com/skeema/knownhosts v1.3.1
	github.com/stretchr/testify v1.9.0
	github.com/trzsz/go-arg v1.5.4
	github.com/trzsz/go-socks5 v0.1.0
	github.com/trzsz/iterm2 v0.1.2
	github.com/trzsz/pageant v0.1.1
	github.com/trzsz/promptui v0.10.8
	github.com/trzsz/ssh_config v1.3.6
	github.com/trzsz/trzsz-go v1.1.8
	github.com/trzsz/tsshd v0.1.4-0.20250419171920-909700138702
	golang.org/x/crypto v0.37.0
	golang.org/x/sys v0.32.0
	golang.org/x/term v0.31.0
)

require (
	github.com/UserExistsError/conpty v0.1.4 // indirect
	github.com/akavel/rsrc v0.10.2 // indirect
	github.com/alexflint/go-scalar v1.2.0 // indirect
	github.com/andybrewer/mack v0.0.0-20220307193339-22e922cc18af // indirect
	github.com/atotto/clipboard v0.1.4 // indirect
	github.com/aymanbagabas/go-osc52/v2 v2.0.1 // indirect
	github.com/boombuler/barcode v1.0.2 // indirect
	github.com/charmbracelet/colorprofile v0.3.0 // indirect
	github.com/charmbracelet/x/ansi v0.8.0 // indirect
	github.com/charmbracelet/x/cellbuf v0.0.13 // indirect
	github.com/charmbracelet/x/term v0.2.1 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/dchest/jsmin v1.0.0 // indirect
	github.com/erikgeiser/coninput v0.0.0-20211004153227-1c3628e74d0f // indirect
	github.com/go-task/slim-sprig/v3 v3.0.0 // indirect
	github.com/google/pprof v0.0.0-20250418163039-24c5476c6587 // indirect
	github.com/gorilla/websocket v1.5.3 // indirect
	github.com/josephspurrier/goversioninfo v1.5.0 // indirect
	github.com/klauspost/compress v1.18.0 // indirect
	github.com/klauspost/cpuid/v2 v2.2.10 // indirect
	github.com/klauspost/reedsolomon v1.12.4 // indirect
	github.com/lucasb-eyer/go-colorful v1.2.0 // indirect
	github.com/mattn/go-localereader v0.0.1 // indirect
	github.com/muesli/ansi v0.0.0-20230316100256-276c6243b2f6 // indirect
	github.com/muesli/cancelreader v0.2.2 // indirect
	github.com/muesli/termenv v0.16.0 // indirect
	github.com/ncruces/zenity v0.10.14 // indirect
	github.com/onsi/ginkgo/v2 v2.23.4 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/quic-go/quic-go v0.50.1-0.20250419021753-b645ce35a210 // indirect
	github.com/randall77/makefat v0.0.0-20210315173500-7ddd0e42c844 // indirect
	github.com/rivo/uniseg v0.4.7 // indirect
	github.com/templexxx/cpu v0.1.1 // indirect
	github.com/templexxx/xorsimd v0.4.3 // indirect
	github.com/tjfoc/gmsm v1.4.1 // indirect
	github.com/xo/terminfo v0.0.0-20220910002029-abceb7e1c41e // indirect
	github.com/xtaci/kcp-go/v5 v5.6.20 // indirect
	github.com/xtaci/smux v1.5.34 // indirect
	go.uber.org/automaxprocs v1.6.0 // indirect
	go.uber.org/mock v0.5.1 // indirect
	golang.org/x/image v0.26.0 // indirect
	golang.org/x/mod v0.24.0 // indirect
	golang.org/x/net v0.39.0 // indirect
	golang.org/x/sync v0.13.0 // indirect
	golang.org/x/text v0.24.0 // indirect
	golang.org/x/tools v0.32.0 // indirect
	google.golang.org/protobuf v1.36.6 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
